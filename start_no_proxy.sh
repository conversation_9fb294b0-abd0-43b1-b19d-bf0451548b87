#!/bin/bash

# Gmail OAuth Manager 启动脚本（无代理模式）
# 用于同时启动前端和后端服务

echo "Gmail OAuth Manager - 启动脚本（无代理模式）"
echo "============================================"

# 检查是否安装了必要的工具
check_command() {
    if ! command -v $1 &> /dev/null; then
        echo "错误: $1 未安装或不在 PATH 中"
        echo "请安装 $1 后重试"
        exit 1
    fi
}

echo "检查依赖..."
check_command "python3"
check_command "pnpm"

# 检查后端环境
echo "检查后端环境..."
if [ ! -f "backend/.env" ]; then
    echo "警告: backend/.env 文件不存在"
    echo "请复制 backend/.env.example 到 backend/.env 并配置你的 Google OAuth 凭据"
    echo "cp backend/.env.example backend/.env"
    exit 1
fi

# 检查前端依赖
echo "检查前端依赖..."
if [ ! -d "node_modules" ]; then
    echo "安装前端依赖..."
    pnpm install
fi

# 检查后端依赖
echo "检查后端依赖..."
cd backend
if ! python3 -c "import flask, flask_cors, google.auth" 2>/dev/null; then
    echo "安装后端依赖..."
    pip3 install -r requirements.txt
fi
cd ..

# 启动后端服务器（普通模式）
echo "启动后端服务器（普通模式）..."
cd backend
python3 run.py &
BACKEND_PID=$!
cd ..

# 等待后端启动
echo "等待后端服务器启动..."
sleep 3

# 测试后端是否正常启动
if curl -s http://localhost:5000/api/health > /dev/null; then
    echo "✓ 后端服务器启动成功"
else
    echo "✗ 后端服务器启动失败"
    kill $BACKEND_PID 2>/dev/null
    exit 1
fi

# 启动前端开发服务器
echo "启动前端开发服务器..."
pnpm dev &
FRONTEND_PID=$!

echo ""
echo "服务启动完成!"
echo "前端地址: http://localhost:3000"
echo "后端地址: http://localhost:5000 (普通模式)"
echo ""
echo "注意: 如果需要代理访问 Google API，请使用 ./start.sh"
echo "按 Ctrl+C 停止所有服务"

# 等待用户中断
trap 'echo "正在停止服务..."; kill $BACKEND_PID $FRONTEND_PID 2>/dev/null; exit 0' INT

# 保持脚本运行
wait
