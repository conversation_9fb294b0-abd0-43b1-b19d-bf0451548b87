#!/usr/bin/env python3
"""
Google OAuth 配置诊断脚本
用于检查 OAuth 配置是否正确
"""

import os
import sys
import requests
from dotenv import load_dotenv

def check_env_file():
    """检查环境变量文件"""
    print("1. 检查环境变量配置...")
    
    if not os.path.exists('.env'):
        print("   ❌ .env 文件不存在")
        print("   💡 请复制 .env.example 到 .env: cp .env.example .env")
        return False
    
    load_dotenv()
    
    required_vars = [
        'GOOGLE_CLIENT_ID',
        'GOOGLE_CLIENT_SECRET',
        'GOOGLE_REDIRECT_URI'
    ]
    
    missing_vars = []
    for var in required_vars:
        value = os.getenv(var)
        if not value or value.startswith('your_'):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"   ❌ 缺少或未配置的环境变量: {', '.join(missing_vars)}")
        print("   💡 请在 .env 文件中设置正确的 Google OAuth 凭据")
        return False
    
    print("   ✅ 环境变量配置正确")
    
    # 检查重定向 URI
    redirect_uri = os.getenv('GOOGLE_REDIRECT_URI')
    if redirect_uri != 'http://localhost:3000':
        print(f"   ⚠️  重定向 URI 是 {redirect_uri}，建议使用 http://localhost:3000")
    
    return True

def check_google_oauth_config():
    """检查 Google OAuth 配置"""
    print("\n2. 检查 Google OAuth 配置...")
    
    client_id = os.getenv('GOOGLE_CLIENT_ID')
    
    if not client_id:
        print("   ❌ 无法获取客户端 ID")
        return False
    
    # 检查客户端 ID 格式
    if not client_id.endswith('.apps.googleusercontent.com'):
        print("   ❌ 客户端 ID 格式不正确")
        print("   💡 Google 客户端 ID 应该以 .apps.googleusercontent.com 结尾")
        return False
    
    print("   ✅ 客户端 ID 格式正确")
    
    # 尝试验证客户端 ID（通过 Google 的公开端点）
    try:
        response = requests.get(
            f"https://oauth2.googleapis.com/tokeninfo?client_id={client_id}",
            timeout=5
        )
        if response.status_code == 400:
            # 这实际上是预期的，因为我们没有提供有效的令牌
            # 但如果客户端 ID 无效，会返回不同的错误
            print("   ✅ 客户端 ID 可以被 Google 识别")
        else:
            print("   ⚠️  无法验证客户端 ID，请确保在 Google Cloud Console 中正确配置")
    except requests.exceptions.RequestException:
        print("   ⚠️  网络错误，无法验证客户端 ID")
    
    return True

def check_backend_server():
    """检查后端服务器"""
    print("\n3. 检查后端服务器...")
    
    try:
        response = requests.get('http://localhost:5000/api/health', timeout=5)
        if response.status_code == 200:
            print("   ✅ 后端服务器正在运行")
            return True
        else:
            print(f"   ❌ 后端服务器响应异常: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("   ❌ 无法连接到后端服务器")
        print("   💡 请启动后端服务器: python run.py")
        return False
    except requests.exceptions.RequestException as e:
        print(f"   ❌ 后端服务器检查失败: {e}")
        return False

def check_frontend_config():
    """检查前端配置"""
    print("\n4. 检查前端配置...")
    
    frontend_file = '../src/components/GoogleLoginButton.tsx'
    if not os.path.exists(frontend_file):
        print("   ❌ 前端配置文件不存在")
        return False
    
    with open(frontend_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    client_id = os.getenv('GOOGLE_CLIENT_ID')
    if client_id and client_id in content:
        print("   ✅ 前端客户端 ID 配置正确")
    else:
        print("   ❌ 前端客户端 ID 配置不匹配")
        print("   💡 请在 GoogleLoginButton.tsx 中更新客户端 ID")
        return False
    
    if 'http://localhost:3000' in content:
        print("   ✅ 前端重定向 URI 配置正确")
    else:
        print("   ⚠️  前端重定向 URI 可能配置不正确")
    
    return True

def main():
    """主函数"""
    print("Google OAuth 配置诊断")
    print("=" * 30)
    
    # 切换到脚本所在目录
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    checks = [
        check_env_file,
        check_google_oauth_config,
        check_backend_server,
        check_frontend_config
    ]
    
    passed = 0
    total = len(checks)
    
    for check in checks:
        try:
            if check():
                passed += 1
        except Exception as e:
            print(f"   ❌ 检查失败: {e}")
    
    print(f"\n诊断结果: {passed}/{total} 项检查通过")
    
    if passed == total:
        print("🎉 配置看起来正确！你可以尝试启动应用了。")
        print("\n启动步骤:")
        print("1. 后端: cd backend && python run.py")
        print("2. 前端: pnpm dev")
        print("3. 访问: http://localhost:3000")
    else:
        print("❌ 存在配置问题，请根据上述提示进行修复。")
        print("📖 详细配置指南请参考 GOOGLE_OAUTH_SETUP.md")
    
    return 0 if passed == total else 1

if __name__ == '__main__':
    sys.exit(main())
