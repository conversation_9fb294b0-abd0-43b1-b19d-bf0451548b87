#!/usr/bin/env python3
"""
HTML 邮件测试脚本
用于测试 HTML 格式邮件发送功能
"""

import requests
import json

def test_html_email():
    """测试发送 HTML 格式邮件"""
    
    # 测试数据
    html_body = """
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>Test Email</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .header { background-color: #4285f4; color: white; padding: 20px; text-align: center; }
            .content { padding: 20px; }
            .button { 
                display: inline-block; 
                background-color: #34a853; 
                color: white; 
                padding: 10px 20px; 
                text-decoration: none; 
                border-radius: 5px; 
                margin: 10px 0;
            }
            .footer { background-color: #f8f9fa; padding: 15px; text-align: center; font-size: 12px; }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>🎉 HTML Email Test</h1>
        </div>
        <div class="content">
            <h2>Hello from Gmail API!</h2>
            <p>This is a <strong>test HTML email</strong> sent using the Gmail API.</p>
            <p>Features demonstrated:</p>
            <ul>
                <li>✅ HTML formatting</li>
                <li>✅ CSS styles</li>
                <li>✅ Emojis and special characters</li>
                <li>✅ Links and buttons</li>
            </ul>
            <p>
                <a href="https://developers.google.com/gmail/api" class="button">
                    Learn More About Gmail API
                </a>
            </p>
            <p>Best regards,<br>
            <em>Your Gmail OAuth App</em></p>
        </div>
        <div class="footer">
            <p>This email was sent using the Gmail API with HTML support.</p>
            <p>© 2025 Gmail OAuth Manager</p>
        </div>
    </body>
    </html>
    """
    
    # 邮件数据
    email_data = {
        "userEmail": "<EMAIL>",  # 替换为实际的用户邮箱
        "to": "<EMAIL>",        # 替换为接收者邮箱
        "subject": "🎨 HTML Email Test - Gmail API",
        "body": html_body,
        "isHtml": True
    }
    
    print("HTML 邮件测试数据:")
    print(f"发送者: {email_data['userEmail']}")
    print(f"接收者: {email_data['to']}")
    print(f"主题: {email_data['subject']}")
    print(f"HTML 格式: {email_data['isHtml']}")
    print(f"邮件内容长度: {len(email_data['body'])} 字符")
    
    # 发送请求
    try:
        response = requests.post(
            'http://localhost:5000/api/email/send',
            json=email_data,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"\n响应状态码: {response.status_code}")
        print(f"响应内容: {response.json()}")
        
        if response.status_code == 200:
            print("✅ HTML 邮件发送成功！")
        else:
            print("❌ HTML 邮件发送失败")
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")

def test_plain_text_email():
    """测试发送纯文本邮件"""
    
    plain_body = """
    Hello!
    
    This is a plain text email sent using the Gmail API.
    
    Features:
    - Simple text formatting
    - No HTML tags
    - Easy to read
    
    Best regards,
    Your Gmail OAuth App
    """
    
    email_data = {
        "userEmail": "<EMAIL>",  # 替换为实际的用户邮箱
        "to": "<EMAIL>",        # 替换为接收者邮箱
        "subject": "📝 Plain Text Email Test - Gmail API",
        "body": plain_body.strip(),
        "isHtml": False
    }
    
    print("\n纯文本邮件测试数据:")
    print(f"发送者: {email_data['userEmail']}")
    print(f"接收者: {email_data['to']}")
    print(f"主题: {email_data['subject']}")
    print(f"HTML 格式: {email_data['isHtml']}")
    
    try:
        response = requests.post(
            'http://localhost:5000/api/email/send',
            json=email_data,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"\n响应状态码: {response.status_code}")
        print(f"响应内容: {response.json()}")
        
        if response.status_code == 200:
            print("✅ 纯文本邮件发送成功！")
        else:
            print("❌ 纯文本邮件发送失败")
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")

def main():
    """主函数"""
    print("Gmail API HTML 邮件测试")
    print("=" * 40)
    
    print("⚠️  注意：请确保:")
    print("1. 后端服务器正在运行 (python run_with_proxy.py)")
    print("2. 已完成 Google OAuth 登录")
    print("3. 修改测试数据中的邮箱地址")
    print()
    
    choice = input("选择测试类型 (1: HTML邮件, 2: 纯文本邮件, 3: 两者都测试): ")
    
    if choice == "1":
        test_html_email()
    elif choice == "2":
        test_plain_text_email()
    elif choice == "3":
        test_html_email()
        test_plain_text_email()
    else:
        print("无效选择")

if __name__ == '__main__':
    main()
