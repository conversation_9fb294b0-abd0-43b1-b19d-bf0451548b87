#!/usr/bin/env python3
"""
Gmail OAuth Backend Server
启动脚本
"""

import os
import sys
from pathlib import Path

def check_requirements():
    """检查是否安装了所需的依赖"""
    try:
        import flask
        import flask_cors
        import google.auth
        import google_auth_oauthlib
        import googleapiclient
        import dotenv
        print("✓ All required packages are installed")
        return True
    except ImportError as e:
        print(f"✗ Missing required package: {e}")
        print("Please install requirements with: pip install -r requirements.txt")
        return False

def check_env_file():
    """检查环境变量文件"""
    env_file = Path('.env')
    env_example = Path('.env.example')
    
    if not env_file.exists():
        if env_example.exists():
            print("✗ .env file not found")
            print("Please copy .env.example to .env and fill in your Google OAuth credentials:")
            print("  cp .env.example .env")
            print("  # Then edit .env with your actual values")
            return False
        else:
            print("✗ Neither .env nor .env.example found")
            return False
    
    print("✓ .env file found")
    return True

def main():
    """主函数"""
    print("Gmail OAuth Backend Server")
    print("=" * 30)
    
    # 检查依赖
    if not check_requirements():
        sys.exit(1)
    
    # 检查环境变量
    if not check_env_file():
        sys.exit(1)
    
    # 启动应用
    print("\nStarting Flask application...")
    try:
        from app import app
        app.run()
    except Exception as e:
        print(f"Failed to start application: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
