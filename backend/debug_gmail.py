#!/usr/bin/env python3
"""
Gmail API 调试脚本
用于测试 Gmail API 连接和诊断超时问题
"""

import os
import sys
import time
import socket
from dotenv import load_dotenv
from google_auth_oauthlib.flow import Flow
from googleapiclient.discovery import build

def test_network_connectivity():
    """测试网络连接"""
    print("1. 测试网络连接...")
    
    test_hosts = [
        ('google.com', 80),
        ('googleapis.com', 443),
        ('accounts.google.com', 443),
        ('gmail.googleapis.com', 443)
    ]
    
    for host, port in test_hosts:
        try:
            socket.setdefaulttimeout(5)
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            result = sock.connect_ex((host, port))
            sock.close()
            
            if result == 0:
                print(f"   ✅ {host}:{port} - 连接成功")
            else:
                print(f"   ❌ {host}:{port} - 连接失败")
        except Exception as e:
            print(f"   ❌ {host}:{port} - 错误: {e}")

def test_oauth_flow():
    """测试 OAuth 流程"""
    print("\n2. 测试 OAuth 配置...")
    
    load_dotenv()
    
    client_id = os.getenv('GOOGLE_CLIENT_ID')
    client_secret = os.getenv('GOOGLE_CLIENT_SECRET')
    redirect_uri = os.getenv('GOOGLE_REDIRECT_URI', 'http://localhost:3000')
    
    if not client_id or not client_secret:
        print("   ❌ 缺少 OAuth 凭据")
        return False
    
    try:
        client_config = {
            "web": {
                "client_id": client_id,
                "client_secret": client_secret,
                "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                "token_uri": "https://oauth2.googleapis.com/token",
                "redirect_uris": [redirect_uri]
            }
        }
        
        flow = Flow.from_client_config(
            client_config,
            scopes=['https://www.googleapis.com/auth/gmail.modify']
        )
        flow.redirect_uri = redirect_uri
        
        print("   ✅ OAuth 流程配置成功")
        return True
        
    except Exception as e:
        print(f"   ❌ OAuth 配置失败: {e}")
        return False

def test_gmail_api_with_timeout():
    """测试 Gmail API 调用的不同超时设置"""
    print("\n3. 测试 Gmail API 超时设置...")
    
    # 这里需要有效的凭据来测试
    print("   ⚠️  需要有效的用户凭据来测试 Gmail API")
    print("   💡 建议在实际登录后查看后端日志来诊断超时问题")
    
    # 测试不同的超时设置
    timeout_values = [5, 10, 15, 30, 60]
    
    for timeout in timeout_values:
        print(f"   📝 建议尝试超时设置: {timeout} 秒")

def test_dns_resolution():
    """测试 DNS 解析"""
    print("\n4. 测试 DNS 解析...")
    
    domains = [
        'googleapis.com',
        'gmail.googleapis.com',
        'accounts.google.com',
        'oauth2.googleapis.com'
    ]
    
    for domain in domains:
        try:
            import socket
            ip = socket.gethostbyname(domain)
            print(f"   ✅ {domain} -> {ip}")
        except Exception as e:
            print(f"   ❌ {domain} - DNS 解析失败: {e}")

def check_system_info():
    """检查系统信息"""
    print("\n5. 系统信息...")
    
    print(f"   Python 版本: {sys.version}")
    print(f"   操作系统: {os.name}")
    
    # 检查已安装的包
    try:
        import google.auth
        print(f"   ✅ google-auth 已安装")
    except ImportError:
        print(f"   ❌ google-auth 未安装")
    
    try:
        import googleapiclient
        print(f"   ✅ google-api-python-client 已安装")
    except ImportError:
        print(f"   ❌ google-api-python-client 未安装")

def suggest_solutions():
    """建议解决方案"""
    print("\n💡 解决超时问题的建议:")
    print("1. 检查网络连接是否稳定")
    print("2. 尝试使用 VPN 或更换网络环境")
    print("3. 增加超时时间设置 (15-30秒)")
    print("4. 检查防火墙设置")
    print("5. 确认 Google API 服务状态: https://status.cloud.google.com/")
    print("6. 尝试重新生成 OAuth 凭据")
    print("7. 检查系统时间是否正确")
    
    print("\n🔧 代码优化建议:")
    print("1. 添加重试机制")
    print("2. 使用连接池")
    print("3. 实现降级方案")
    print("4. 添加更详细的错误日志")

def main():
    """主函数"""
    print("Gmail API 调试工具")
    print("=" * 40)
    
    # 切换到脚本所在目录
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    test_network_connectivity()
    test_oauth_flow()
    test_gmail_api_with_timeout()
    test_dns_resolution()
    check_system_info()
    suggest_solutions()
    
    print("\n🎯 下一步:")
    print("1. 启动后端服务器: python run.py")
    print("2. 尝试登录并查看详细日志")
    print("3. 如果仍有问题，请提供具体的错误信息")

if __name__ == '__main__':
    main()
