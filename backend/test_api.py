#!/usr/bin/env python3
"""
API 测试脚本
用于测试后端 API 端点是否正常工作
"""

import requests
import json
import sys

BASE_URL = 'http://localhost:5000'

def test_health_check():
    """测试健康检查端点"""
    print("Testing health check endpoint...")
    try:
        response = requests.get(f'{BASE_URL}/api/health')
        if response.status_code == 200:
            data = response.json()
            print(f"✓ Health check passed: {data['message']}")
            return True
        else:
            print(f"✗ Health check failed: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("✗ Cannot connect to server. Make sure the backend is running.")
        return False
    except Exception as e:
        print(f"✗ Health check error: {e}")
        return False

def test_cors():
    """测试 CORS 配置"""
    print("Testing CORS configuration...")
    try:
        headers = {
            'Origin': 'http://localhost:3000',
            'Access-Control-Request-Method': 'POST',
            'Access-Control-Request-Headers': 'Content-Type'
        }
        response = requests.options(f'{BASE_URL}/api/health', headers=headers)
        
        cors_headers = response.headers
        if 'Access-Control-Allow-Origin' in cors_headers:
            print("✓ CORS is properly configured")
            return True
        else:
            print("✗ CORS headers not found")
            return False
    except Exception as e:
        print(f"✗ CORS test error: {e}")
        return False

def test_auth_endpoint():
    """测试认证端点（不进行实际认证）"""
    print("Testing auth endpoint structure...")
    try:
        # 发送无效请求来测试端点是否存在
        response = requests.post(f'{BASE_URL}/api/auth/google', json={})
        
        if response.status_code == 400:
            data = response.json()
            if 'message' in data:
                print("✓ Auth endpoint is responding correctly")
                return True
        
        print(f"✗ Unexpected auth endpoint response: {response.status_code}")
        return False
    except Exception as e:
        print(f"✗ Auth endpoint test error: {e}")
        return False

def test_email_endpoints():
    """测试邮件相关端点（不进行实际操作）"""
    print("Testing email endpoints structure...")
    
    endpoints = [
        '/api/email/send',
        '/api/email/list',
        '/api/email/test123'  # 测试邮件详情端点
    ]
    
    all_passed = True
    
    for endpoint in endpoints:
        try:
            response = requests.post(f'{BASE_URL}{endpoint}', json={})
            
            # 期望返回 400 或 401 状态码（因为没有提供有效数据）
            if response.status_code in [400, 401]:
                print(f"✓ {endpoint} is responding correctly")
            else:
                print(f"✗ {endpoint} unexpected response: {response.status_code}")
                all_passed = False
        except Exception as e:
            print(f"✗ {endpoint} test error: {e}")
            all_passed = False
    
    return all_passed

def main():
    """主测试函数"""
    print("Gmail OAuth Backend API Tests")
    print("=" * 40)
    
    tests = [
        ("Health Check", test_health_check),
        ("CORS Configuration", test_cors),
        ("Auth Endpoint", test_auth_endpoint),
        ("Email Endpoints", test_email_endpoints)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        if test_func():
            passed += 1
        print("-" * 20)
    
    print(f"\nTest Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✓ All tests passed! Backend is ready.")
        return 0
    else:
        print("✗ Some tests failed. Please check the backend configuration.")
        return 1

if __name__ == '__main__':
    sys.exit(main())
