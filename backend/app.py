import os
import json
import base64
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from flask import Flask, request, jsonify
from flask_cors import CORS
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import Flow
from googleapiclient.discovery import build
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

app = Flask(__name__)
app.secret_key = os.getenv('FLASK_SECRET_KEY', 'your-secret-key')

# 启用 CORS
CORS(app, origins=['http://localhost:3000'])

# Google OAuth 配置
SCOPES = ['https://www.googleapis.com/auth/gmail.modify']
CLIENT_ID = os.getenv('GOOGLE_CLIENT_ID')
CLIENT_SECRET = os.getenv('GOOGLE_CLIENT_SECRET')
REDIRECT_URI = os.getenv('GOOGLE_REDIRECT_URI', 'http://localhost:5000/auth/callback')

# 存储用户凭据（在生产环境中应使用数据库）
user_credentials = {}

def create_oauth_flow():
    """创建 OAuth 流程"""
    client_config = {
        "web": {
            "client_id": CLIENT_ID,
            "client_secret": CLIENT_SECRET,
            "auth_uri": "https://accounts.google.com/o/oauth2/auth",
            "token_uri": "https://oauth2.googleapis.com/token",
            "redirect_uris": [REDIRECT_URI]
        }
    }
    
    flow = Flow.from_client_config(
        client_config,
        scopes=SCOPES
    )
    flow.redirect_uri = REDIRECT_URI
    return flow

@app.route('/api/health', methods=['GET'])
def health_check():
    """健康检查端点"""
    return jsonify({"status": "OK", "message": "Backend server is running"})

@app.route('/api/auth/google', methods=['POST'])
def google_auth():
    """处理 Google OAuth 授权码"""
    try:
        data = request.get_json()
        auth_code = data.get('code')
        
        if not auth_code:
            return jsonify({"success": False, "message": "Authorization code is required"}), 400
        
        # 创建 OAuth 流程
        flow = create_oauth_flow()
        
        # 使用授权码获取访问令牌
        flow.fetch_token(code=auth_code)
        
        # 获取凭据
        credentials = flow.credentials
        
        # 使用凭据获取用户信息
        service = build('gmail', 'v1', credentials=credentials)
        profile = service.users().getProfile(userId='me').execute()
        
        user_email = profile['emailAddress']
        
        # 存储用户凭据
        user_credentials[user_email] = {
            'token': credentials.token,
            'refresh_token': credentials.refresh_token,
            'token_uri': credentials.token_uri,
            'client_id': credentials.client_id,
            'client_secret': credentials.client_secret,
            'scopes': credentials.scopes
        }
        
        # 构造用户信息（模拟从 Google+ API 获取的信息）
        user_info = {
            'name': user_email.split('@')[0],  # 简单从邮箱提取用户名
            'email': user_email,
            'picture': 'https://via.placeholder.com/50'  # 占位符头像
        }
        
        return jsonify({
            "success": True,
            "user": user_info,
            "message": "Authentication successful"
        })
        
    except Exception as e:
        print(f"Google auth error: {str(e)}")
        return jsonify({"success": False, "message": f"Authentication failed: {str(e)}"}), 401

def get_user_credentials(user_email):
    """获取用户凭据"""
    if user_email not in user_credentials:
        return None
    
    cred_data = user_credentials[user_email]
    credentials = Credentials(
        token=cred_data['token'],
        refresh_token=cred_data['refresh_token'],
        token_uri=cred_data['token_uri'],
        client_id=cred_data['client_id'],
        client_secret=cred_data['client_secret'],
        scopes=cred_data['scopes']
    )
    
    # 如果令牌过期，尝试刷新
    if credentials.expired and credentials.refresh_token:
        credentials.refresh(Request())
        # 更新存储的令牌
        user_credentials[user_email]['token'] = credentials.token
    
    return credentials

@app.route('/api/email/send', methods=['POST'])
def send_email():
    """发送邮件"""
    try:
        data = request.get_json()
        user_email = data.get('userEmail')
        to_email = data.get('to')
        subject = data.get('subject')
        body = data.get('body')

        if not all([user_email, to_email, subject, body]):
            return jsonify({"success": False, "message": "Missing required fields"}), 400

        # 获取用户凭据
        credentials = get_user_credentials(user_email)
        if not credentials:
            return jsonify({"success": False, "message": "User not authenticated"}), 401

        # 创建 Gmail 服务
        service = build('gmail', 'v1', credentials=credentials)

        # 创建邮件消息
        message = MIMEMultipart()
        message['to'] = to_email
        message['subject'] = subject
        message.attach(MIMEText(body, 'plain'))

        # 编码邮件
        raw_message = base64.urlsafe_b64encode(message.as_bytes()).decode()

        # 发送邮件
        send_result = service.users().messages().send(
            userId='me',
            body={'raw': raw_message}
        ).execute()

        return jsonify({
            "success": True,
            "message": f"Email sent successfully. Message ID: {send_result['id']}"
        })

    except Exception as e:
        print(f"Send email error: {str(e)}")
        return jsonify({"success": False, "message": f"Failed to send email: {str(e)}"}), 500

@app.route('/api/email/list', methods=['POST'])
def list_emails():
    """获取邮件列表"""
    try:
        data = request.get_json()
        user_email = data.get('userEmail')
        max_results = data.get('maxResults', 10)
        query = data.get('query', '')  # 可选的搜索查询

        if not user_email:
            return jsonify({"success": False, "message": "User email is required"}), 400

        # 获取用户凭据
        credentials = get_user_credentials(user_email)
        if not credentials:
            return jsonify({"success": False, "message": "User not authenticated"}), 401

        # 创建 Gmail 服务
        service = build('gmail', 'v1', credentials=credentials)

        # 获取邮件列表
        results = service.users().messages().list(
            userId='me',
            maxResults=max_results,
            q=query
        ).execute()

        messages = results.get('messages', [])

        # 获取每封邮件的详细信息
        email_list = []
        for message in messages:
            msg = service.users().messages().get(
                userId='me',
                id=message['id'],
                format='metadata',
                metadataHeaders=['From', 'Subject', 'Date']
            ).execute()

            headers = msg['payload'].get('headers', [])
            email_info = {
                'id': message['id'],
                'snippet': msg.get('snippet', ''),
                'from': '',
                'subject': '',
                'date': ''
            }

            for header in headers:
                name = header['name'].lower()
                if name == 'from':
                    email_info['from'] = header['value']
                elif name == 'subject':
                    email_info['subject'] = header['value']
                elif name == 'date':
                    email_info['date'] = header['value']

            email_list.append(email_info)

        return jsonify({
            "success": True,
            "emails": email_list,
            "total": len(email_list)
        })

    except Exception as e:
        print(f"List emails error: {str(e)}")
        return jsonify({"success": False, "message": f"Failed to fetch emails: {str(e)}"}), 500

@app.route('/api/email/<message_id>', methods=['POST'])
def get_email_detail(message_id):
    """获取单封邮件的详细内容"""
    try:
        data = request.get_json()
        user_email = data.get('userEmail')

        if not user_email:
            return jsonify({"success": False, "message": "User email is required"}), 400

        # 获取用户凭据
        credentials = get_user_credentials(user_email)
        if not credentials:
            return jsonify({"success": False, "message": "User not authenticated"}), 401

        # 创建 Gmail 服务
        service = build('gmail', 'v1', credentials=credentials)

        # 获取邮件详细信息
        message = service.users().messages().get(
            userId='me',
            id=message_id,
            format='full'
        ).execute()

        # 解析邮件内容
        payload = message['payload']
        headers = payload.get('headers', [])

        email_detail = {
            'id': message['id'],
            'snippet': message.get('snippet', ''),
            'from': '',
            'to': '',
            'subject': '',
            'date': '',
            'body': ''
        }

        # 提取头部信息
        for header in headers:
            name = header['name'].lower()
            if name == 'from':
                email_detail['from'] = header['value']
            elif name == 'to':
                email_detail['to'] = header['value']
            elif name == 'subject':
                email_detail['subject'] = header['value']
            elif name == 'date':
                email_detail['date'] = header['value']

        # 提取邮件正文
        def extract_body(payload):
            body = ""
            if 'parts' in payload:
                for part in payload['parts']:
                    if part['mimeType'] == 'text/plain':
                        data = part['body']['data']
                        body = base64.urlsafe_b64decode(data).decode('utf-8')
                        break
                    elif part['mimeType'] == 'text/html':
                        data = part['body']['data']
                        body = base64.urlsafe_b64decode(data).decode('utf-8')
            else:
                if payload['mimeType'] == 'text/plain':
                    data = payload['body']['data']
                    body = base64.urlsafe_b64decode(data).decode('utf-8')
            return body

        email_detail['body'] = extract_body(payload)

        return jsonify({
            "success": True,
            "email": email_detail
        })

    except Exception as e:
        print(f"Get email detail error: {str(e)}")
        return jsonify({"success": False, "message": f"Failed to fetch email detail: {str(e)}"}), 500

if __name__ == '__main__':
    port = int(os.getenv('PORT', 5000))
    host = os.getenv('HOST', 'localhost')
    debug = os.getenv('FLASK_DEBUG', 'True').lower() == 'true'

    print(f"Starting Flask server on {host}:{port}")
    print(f"Health check: http://{host}:{port}/api/health")

    app.run(host=host, port=port, debug=debug)
