#!/usr/bin/env python3
"""
带代理的 Flask 应用启动脚本
"""

import os
import sys
from dotenv import load_dotenv

def setup_proxy_environment():
    """设置代理环境变量"""
    print("设置代理环境变量...")
    
    # 设置代理环境变量
    proxy_settings = {
        'http_proxy': 'http://127.0.0.1:1087',
        'https_proxy': 'http://127.0.0.1:1087',
        'HTTP_PROXY': 'http://127.0.0.1:1087',
        'HTTPS_PROXY': 'http://127.0.0.1:1087'
    }
    
    for key, value in proxy_settings.items():
        os.environ[key] = value
        print(f"  {key} = {value}")
    
    print("代理环境变量设置完成")

def test_proxy():
    """测试代理连接"""
    print("\n测试代理连接...")
    
    try:
        import requests
        
        proxies = {
            'http': os.environ.get('http_proxy'),
            'https': os.environ.get('https_proxy')
        }
        
        response = requests.get('https://www.google.com', 
                              proxies=proxies, 
                              timeout=10)
        
        if response.status_code == 200:
            print("✅ 代理连接测试成功")
            return True
        else:
            print(f"❌ 代理连接测试失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 代理连接测试失败: {e}")
        return False

def main():
    """主函数"""
    print("Gmail OAuth Backend - 代理模式启动")
    print("=" * 40)
    
    # 加载环境变量
    load_dotenv()
    
    # 设置代理
    setup_proxy_environment()
    
    # 测试代理
    if not test_proxy():
        print("\n⚠️  代理测试失败，但仍将尝试启动应用")
        print("请确保代理服务器 127.0.0.1:1087 正在运行")
    
    print("\n启动 Flask 应用...")
    
    try:
        # 导入并启动应用
        from app import app
        
        port = int(os.getenv('PORT', 5000))
        host = os.getenv('HOST', 'localhost')
        debug = os.getenv('FLASK_DEBUG', 'True').lower() == 'true'
        
        print(f"服务器地址: http://{host}:{port}")
        print("代理设置已启用")
        print("按 Ctrl+C 停止服务器")
        
        app.run(host=host, port=port, debug=debug)
        
    except Exception as e:
        print(f"启动失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
