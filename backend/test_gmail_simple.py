#!/usr/bin/env python3
"""
简单的 Gmail API 测试脚本
用于验证代理和 API 调用是否正常
"""

import os
import socket
from dotenv import load_dotenv
from google_auth_oauthlib.flow import Flow
from googleapiclient.discovery import build

def setup_proxy():
    """设置代理环境变量"""
    # 设置代理
    os.environ['http_proxy'] = 'http://127.0.0.1:1087'
    os.environ['https_proxy'] = 'http://127.0.0.1:1087'
    os.environ['HTTP_PROXY'] = 'http://127.0.0.1:1087'
    os.environ['HTTPS_PROXY'] = 'http://127.0.0.1:1087'
    
    print("代理环境变量已设置:")
    print(f"  http_proxy: {os.environ.get('http_proxy')}")
    print(f"  https_proxy: {os.environ.get('https_proxy')}")

def test_oauth_flow():
    """测试 OAuth 流程创建"""
    print("\n测试 OAuth 流程...")
    
    load_dotenv()
    
    client_id = os.getenv('GOOGLE_CLIENT_ID')
    client_secret = os.getenv('GOOGLE_CLIENT_SECRET')
    redirect_uri = os.getenv('GOOGLE_REDIRECT_URI', 'http://localhost:3000')
    
    if not client_id or not client_secret:
        print("❌ 缺少 OAuth 凭据")
        return False
    
    try:
        client_config = {
            "web": {
                "client_id": client_id,
                "client_secret": client_secret,
                "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                "token_uri": "https://oauth2.googleapis.com/token",
                "redirect_uris": [redirect_uri]
            }
        }
        
        scopes = [
            'https://www.googleapis.com/auth/gmail.modify',
            'https://www.googleapis.com/auth/userinfo.email',
            'openid',
            'https://www.googleapis.com/auth/userinfo.profile'
        ]
        
        flow = Flow.from_client_config(client_config, scopes=scopes)
        flow.redirect_uri = redirect_uri
        
        print("✅ OAuth 流程创建成功")
        return True
        
    except Exception as e:
        print(f"❌ OAuth 流程创建失败: {e}")
        return False

def test_gmail_service_creation():
    """测试 Gmail 服务创建（需要有效凭据）"""
    print("\n测试 Gmail 服务创建...")
    
    try:
        # 设置超时
        socket.setdefaulttimeout(30)
        
        # 这里需要有效的凭据来测试
        print("⚠️  需要有效的 OAuth 凭据来测试 Gmail 服务创建")
        print("   建议在实际登录流程中测试")
        
        return True
        
    except Exception as e:
        print(f"❌ Gmail 服务创建失败: {e}")
        return False

def test_network_with_proxy():
    """测试网络连接（使用代理）"""
    print("\n测试网络连接...")
    
    try:
        import requests
        
        # 使用代理测试连接
        proxies = {
            'http': 'http://127.0.0.1:1087',
            'https': 'http://127.0.0.1:1087'
        }
        
        test_url = 'https://www.googleapis.com'
        response = requests.get(test_url, proxies=proxies, timeout=10)
        
        if response.status_code == 200:
            print(f"✅ 代理连接测试成功: {test_url}")
            return True
        else:
            print(f"❌ 代理连接测试失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 网络连接测试失败: {e}")
        return False

def main():
    """主函数"""
    print("Gmail API 简单测试")
    print("=" * 30)
    
    # 设置代理
    setup_proxy()
    
    # 运行测试
    tests = [
        ("网络连接", test_network_with_proxy),
        ("OAuth 流程", test_oauth_flow),
        ("Gmail 服务", test_gmail_service_creation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        if test_func():
            passed += 1
    
    print(f"\n测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("✅ 所有测试通过！可以尝试启动应用")
    else:
        print("❌ 部分测试失败，请检查配置")
    
    print("\n启动建议:")
    print("1. 确保代理服务器运行在 127.0.0.1:1087")
    print("2. 使用以下命令启动应用:")
    print("   python run_with_proxy.py")

if __name__ == '__main__':
    main()
