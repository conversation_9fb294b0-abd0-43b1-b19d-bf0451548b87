#!/usr/bin/env python3
"""
代理测试脚本
用于测试代理设置是否正常工作
"""

import os
import requests
import socket
from dotenv import load_dotenv

def test_proxy_connection():
    """测试代理连接"""
    print("测试代理连接...")
    
    # 设置代理
    proxies = {
        'http': 'http://127.0.0.1:1087',
        'https': 'http://127.0.0.1:1087'
    }
    
    test_urls = [
        'https://www.google.com',
        'https://googleapis.com',
        'https://gmail.googleapis.com'
    ]
    
    for url in test_urls:
        try:
            print(f"测试 {url}...")
            
            # 不使用代理
            try:
                response = requests.get(url, timeout=5)
                print(f"  直连: ✅ {response.status_code}")
            except Exception as e:
                print(f"  直连: ❌ {e}")
            
            # 使用代理
            try:
                response = requests.get(url, proxies=proxies, timeout=10)
                print(f"  代理: ✅ {response.status_code}")
            except Exception as e:
                print(f"  代理: ❌ {e}")
                
        except Exception as e:
            print(f"  错误: {e}")
        
        print()

def test_google_api_with_proxy():
    """测试 Google API 代理连接"""
    print("测试 Google API 代理连接...")
    
    load_dotenv()
    
    # 设置环境变量代理
    os.environ['http_proxy'] = 'http://127.0.0.1:1087'
    os.environ['https_proxy'] = 'http://127.0.0.1:1087'
    
    try:
        from googleapiclient.discovery import build
        from google.oauth2.credentials import Credentials
        
        print("Google API 库导入成功")
        
        # 这里需要有效的凭据来测试
        print("需要有效的 OAuth 凭据来完整测试 Gmail API")
        
    except ImportError as e:
        print(f"Google API 库导入失败: {e}")
    except Exception as e:
        print(f"测试失败: {e}")

def check_proxy_settings():
    """检查代理设置"""
    print("检查代理设置...")
    
    env_vars = ['http_proxy', 'https_proxy', 'HTTP_PROXY', 'HTTPS_PROXY', 'ALL_PROXY']
    
    for var in env_vars:
        value = os.getenv(var)
        if value:
            print(f"  {var}: {value}")
        else:
            print(f"  {var}: 未设置")

def main():
    """主函数"""
    print("代理测试工具")
    print("=" * 30)
    
    check_proxy_settings()
    print()
    test_proxy_connection()
    test_google_api_with_proxy()
    
    print("\n建议:")
    print("1. 确保代理服务器 127.0.0.1:1087 正在运行")
    print("2. 在启动 Flask 应用前设置环境变量:")
    print("   export http_proxy=http://127.0.0.1:1087")
    print("   export https_proxy=http://127.0.0.1:1087")
    print("3. 或者在 .env 文件中添加:")
    print("   http_proxy=http://127.0.0.1:1087")
    print("   https_proxy=http://127.0.0.1:1087")

if __name__ == '__main__':
    main()
