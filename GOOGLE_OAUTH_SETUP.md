# Google OAuth 设置指南

## 步骤 1：创建 Google Cloud 项目

1. 访问 [Google Cloud Console](https://console.cloud.google.com/)
2. 点击项目选择器，然后点击"新建项目"
3. 输入项目名称（例如：gmail-oauth-app）
4. 点击"创建"

## 步骤 2：启用 Gmail API

1. 在 Google Cloud Console 中，确保选择了正确的项目
2. 导航到"API 和服务" > "库"
3. 搜索"Gmail API"
4. 点击"Gmail API"，然后点击"启用"

## 步骤 3：配置 OAuth 同意屏幕

1. 导航到"API 和服务" > "OAuth 同意屏幕"
2. 选择"外部"用户类型（除非你有 Google Workspace 账户）
3. 点击"创建"
4. 填写必需信息：
   - **应用名称**：Gmail OAuth App
   - **用户支持电子邮件**：你的邮箱
   - **开发者联系信息**：你的邮箱
5. 点击"保存并继续"
6. 在"范围"页面，点击"添加或移除范围"
7. 搜索并添加以下范围：
   - `https://www.googleapis.com/auth/gmail.modify`
8. 点击"保存并继续"
9. 在"测试用户"页面，添加你要测试的 Gmail 账户
10. 点击"保存并继续"

## 步骤 4：创建 OAuth 2.0 客户端 ID

1. 导航到"API 和服务" > "凭据"
2. 点击"+ 创建凭据" > "OAuth 2.0 客户端 ID"
3. 选择应用类型："Web 应用"
4. 输入名称：Gmail OAuth Client
5. 在"已获授权的重定向 URI"中添加：
   ```
   http://localhost:3000
   ```
6. 点击"创建"
7. 复制显示的客户端 ID 和客户端密钥

## 步骤 5：配置应用

### 后端配置

1. 复制环境变量文件：
   ```bash
   cp backend/.env.example backend/.env
   ```

2. 编辑 `backend/.env` 文件：
   ```env
   GOOGLE_CLIENT_ID=你的客户端ID
   GOOGLE_CLIENT_SECRET=你的客户端密钥
   GOOGLE_REDIRECT_URI=http://localhost:3000
   FLASK_SECRET_KEY=你的密钥
   ```

### 前端配置

编辑 `src/components/GoogleLoginButton.tsx`，将客户端 ID 替换为你的实际客户端 ID：

```typescript
<GoogleOAuthProvider clientId="你的客户端ID">
```

## 步骤 6：测试配置

1. 启动后端服务器：
   ```bash
   cd backend
   python run.py
   ```

2. 启动前端服务器：
   ```bash
   pnpm dev
   ```

3. 访问 http://localhost:3000
4. 点击"Sign in with Google"按钮
5. 完成 Google OAuth 流程

## 常见问题

### redirect_uri_mismatch 错误

如果遇到此错误，请检查：

1. Google Cloud Console 中的重定向 URI 是否为 `http://localhost:3000`
2. 前端代码中的 `redirect_uri` 是否为 `http://localhost:3000`
3. 后端 `.env` 文件中的 `GOOGLE_REDIRECT_URI` 是否为 `http://localhost:3000`

### access_denied 错误

如果遇到此错误，请检查：

1. 是否在 OAuth 同意屏幕中添加了测试用户
2. 是否启用了 Gmail API
3. 是否添加了正确的 OAuth 范围

### 权限不足错误

如果遇到权限错误，请确保：

1. OAuth 范围包含 `https://www.googleapis.com/auth/gmail.modify`
2. 用户已授权应用访问 Gmail

## 生产环境注意事项

1. 将重定向 URI 更改为你的生产域名
2. 将 OAuth 同意屏幕从"测试"模式改为"生产"模式
3. 添加隐私政策和服务条款链接
4. 使用环境变量管理敏感信息
