# Gmail OAuth Manager

一个完整的 Gmail 邮件管理应用，支持邮件发送和读取功能。

## 功能特性

- ✅ Google OAuth 2.0 认证
- ✅ Gmail 邮件发送
- ✅ Gmail 邮件列表查看
- ✅ Gmail 邮件详情查看
- ✅ 现代化的 React + TypeScript 前端
- ✅ Python Flask 后端
- ✅ 完整的错误处理和用户反馈

## 技术栈

### 前端
- React 19 + TypeScript
- @react-oauth/google (Google OAuth)
- Axios (HTTP 客户端)
- Rsbuild (构建工具)

### 后端
- Python 3.8+
- Flask (Web 框架)
- Google API Client Library
- Flask-CORS (跨域支持)

## 快速开始

### 1. 克隆项目

```bash
git clone <repository-url>
cd gmail-oauth
```

### 2. 设置 Google OAuth

1. 访问 [Google Cloud Console](https://console.cloud.google.com/)
2. 创建新项目或选择现有项目
3. 启用 Gmail API
4. 创建 OAuth 2.0 客户端 ID
5. 设置授权重定向 URI：`http://localhost:5000/auth/callback`
6. 记录客户端 ID 和客户端密钥

### 3. 配置后端

```bash
cd backend

# 安装 Python 依赖
pip install -r requirements.txt

# 复制环境变量模板
cp .env.example .env

# 编辑 .env 文件，填入你的 Google OAuth 凭据
# GOOGLE_CLIENT_ID=your_client_id_here
# GOOGLE_CLIENT_SECRET=your_client_secret_here
```

### 4. 配置前端

更新 `src/components/GoogleLoginButton.tsx` 中的客户端 ID：

```typescript
<GoogleOAuthProvider clientId="your_google_client_id_here">
```

**重要提示**：确保在 Google Cloud Console 中设置的授权重定向 URI 包含：
- `http://localhost:3000` (用于前端开发)
- `http://localhost:5000/auth/callback` (用于后端回调)

### 5. 启动应用

#### 启动后端服务器

```bash
cd backend
python run.py
```

后端服务器将在 `http://127.0.0.1:5000` 启动

#### 启动前端开发服务器

```bash
# 在项目根目录
pnpm install
pnpm dev
```

前端应用将在 `http://localhost:3000` 启动

## 使用说明

1. 打开浏览器访问 `http://localhost:3000`
2. 点击 "Sign in with Google" 按钮
3. 完成 Google OAuth 授权流程
4. 登录成功后，你可以：
   - 查看收件箱中的邮件列表
   - 点击邮件查看详细内容
   - 切换到 "Compose" 标签页发送新邮件

## API 端点

### 认证
- `POST /api/auth/google` - Google OAuth 认证

### 邮件操作
- `POST /api/email/send` - 发送邮件
- `POST /api/email/list` - 获取邮件列表
- `POST /api/email/<message_id>` - 获取邮件详情

### 健康检查
- `GET /api/health` - 服务器健康检查

## 项目结构

```
gmail-oauth/
├── src/                    # 前端源码
│   ├── components/         # React 组件
│   │   ├── GoogleLoginButton.tsx
│   │   └── EmailManager.tsx
│   ├── types.ts           # TypeScript 类型定义
│   └── App.tsx            # 主应用组件
├── backend/               # 后端源码
│   ├── app.py            # Flask 应用
│   ├── run.py            # 启动脚本
│   ├── requirements.txt  # Python 依赖
│   └── .env.example      # 环境变量模板
└── README.md             # 项目说明
```

## 开发说明

### 前端开发

```bash
# 安装依赖
pnpm install

# 启动开发服务器
pnpm dev

# 构建生产版本
pnpm build
```

### 后端开发

```bash
cd backend

# 安装依赖
pip install -r requirements.txt

# 启动开发服务器
python run.py
```

## 故障排除

### 常见问题

1. **OAuth 错误**: 确保 Google Cloud Console 中的重定向 URI 设置正确
2. **CORS 错误**: 确保后端服务器正在运行且 CORS 配置正确
3. **API 权限错误**: 确保在 Google Cloud Console 中启用了 Gmail API

### 调试

- 检查浏览器控制台的错误信息
- 检查后端服务器的日志输出
- 确认环境变量配置正确

## 许可证

MIT License
