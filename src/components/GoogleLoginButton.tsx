import React, { useState } from 'react';
import { GoogleOAuthProvider, useGoogleLogin } from '@react-oauth/google';
import axios from 'axios';
import type { User } from '../types';
import EmailManager from './EmailManager';

// 内部登录组件，必须在 GoogleOAuthProvider 内部使用
const LoginComponent: React.FC = () => {
  const [user, setUser] = useState<User | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  const login = useGoogleLogin({
    onSuccess: async (response) => {
      console.log('Authorization code:', response.code);
      setLoading(true);
      setError(null);
      try {
        const res = await axios.post<{ success: boolean; user: User; token?: string }>(
          'http://localhost:5000/api/auth/google',
          { code: response.code },
          { headers: { 'Content-Type': 'application/json' } }
        );
        console.log('Backend response:', res.data);
        if (res.data.success) {
          setUser(res.data.user);
        } else {
          setError('Authentication failed. Please try again.');
        }
      } catch (err: any) {
        console.error('Backend auth failed:', err.response?.data || err.message);
        setError('Login failed. Please try again.');
      } finally {
        setLoading(false);
      }
    },
    onError: (error) => {
      console.error('Google login failed:', error);
      setError('Google login failed.');
      setLoading(false);
    },
    flow: 'auth-code',
    scope: 'https://www.googleapis.com/auth/gmail.modify',
    redirect_uri: 'http://localhost:5000/auth/callback',
  });

  const handleSignOut = () => {
    setUser(null);
    setError(null);
    console.log('User signed out');
  };

  const styles = {
    container: {
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      minHeight: '100vh',
      backgroundColor: '#f5f5f5',
      fontFamily: 'Arial, sans-serif'
    },
    loginCard: {
      backgroundColor: 'white',
      padding: '40px',
      borderRadius: '10px',
      boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
      textAlign: 'center' as const,
      maxWidth: '400px',
      width: '100%'
    },
    title: {
      marginBottom: '30px',
      color: '#333',
      fontSize: '24px'
    },
    button: {
      backgroundColor: '#4285f4',
      color: 'white',
      border: 'none',
      padding: '12px 24px',
      borderRadius: '5px',
      fontSize: '16px',
      cursor: 'pointer',
      transition: 'background-color 0.3s'
    },
    buttonDisabled: {
      backgroundColor: '#ccc',
      cursor: 'not-allowed'
    },
    error: {
      color: '#dc3545',
      marginTop: '15px',
      padding: '10px',
      backgroundColor: '#f8d7da',
      border: '1px solid #f5c6cb',
      borderRadius: '4px'
    },
    loading: {
      color: '#666',
      marginTop: '15px'
    }
  };

  return (
    <>
      {!user ? (
        <div style={styles.container}>
          <div style={styles.loginCard}>
            <h1 style={styles.title}>Gmail Manager</h1>
            <p style={{ marginBottom: '30px', color: '#666' }}>
              Sign in with your Google account to manage your Gmail
            </p>
            <button
              onClick={() => login()}
              disabled={loading}
              style={{
                ...styles.button,
                ...(loading ? styles.buttonDisabled : {})
              }}
              onMouseEnter={(e) => {
                if (!loading) {
                  e.currentTarget.style.backgroundColor = '#3367d6';
                }
              }}
              onMouseLeave={(e) => {
                if (!loading) {
                  e.currentTarget.style.backgroundColor = '#4285f4';
                }
              }}
            >
              {loading ? 'Signing in...' : 'Sign in with Google'}
            </button>
            {error && (
              <div style={styles.error}>
                {error}
              </div>
            )}
          </div>
        </div>
      ) : (
        <EmailManager user={user} onSignOut={handleSignOut} />
      )}
    </>
  );
};

// 主组件，包含 GoogleOAuthProvider
const GoogleLoginButton: React.FC = () => {
  return (
    <GoogleOAuthProvider clientId="194042618156-ooh6jq5ef8ge8477a6o8r488eqr131jo.apps.googleusercontent.com">
      <LoginComponent />
    </GoogleOAuthProvider>
  );
};

export default GoogleLoginButton;