import React, { useState } from 'react';
import { GoogleOAuthProvider, useGoogleLogin } from '@react-oauth/google';
import axios from 'axios';

interface User {
  name: string;
  email: string;
  picture: string;
}

const GoogleLoginButton: React.FC = () => {
  const [user, setUser] = useState<User | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [emailForm, setEmailForm] = useState({
    to: '',
    subject: '',
    body: '',
  });
  const [sendStatus, setSendStatus] = useState<string | null>(null);

  const login = useGoogleLogin({
    onSuccess: async (response) => {
      console.log('Authorization code:', response.code);
      try {
        const res = await axios.post<{ success: boolean; user: User; token?: string }>(
          'http://127.0.0.1:5000/api/auth/google',
          { code: response.code },
          { headers: { 'Content-Type': 'application/json' } }
        );
        console.log('Backend response:', res.data);
        setUser(res.data.user);
        setError(null);
      } catch (err: any) {
        console.error('Backend auth failed:', err.response?.data || err.message);
        setError('Login failed. Please try again.');
      }
    },
    onError: (error) => {
      console.error('Google login failed:', error);
      setError('Google login failed.');
    },
    flow: 'auth-code',
    scope: 'https://www.googleapis.com/auth/gmail.modify',
    redirect_uri: 'http://localhost:5000/auth/callback', // Match backend and Google Console
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setEmailForm((prev) => ({ ...prev, [name]: value }));
  };

  const handleSendEmail = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user) {
      setError('Please login first.');
      return;
    }
    try {
      const res = await axios.post<{ success: boolean; message: string }>(
        'http://127.0.0.1:5000/api/email/send',
        {
          to: emailForm.to,
          subject: emailForm.subject,
          body: emailForm.body,
          userEmail: user.email,
        },
        { headers: { 'Content-Type': 'application/json' } }
      );
      setSendStatus(`Email sent successfully: ${res.data.message}`);
      setEmailForm({ to: '', subject: '', body: '' });
    } catch (err: any) {
      console.error('Email sending failed:', err.response?.data || err.message);
      setSendStatus('Failed to send email. Please try again.');
    }
  };

  const handleSignOut = () => {
    setUser(null);
    setSendStatus(null);
    console.log('User signed out');
  };

  return (
    <GoogleOAuthProvider clientId="194042618156-ooh6jq5ef8ge8477a6o8r488eqr131jo.apps.googleusercontent.com">
      <div style={{ padding: '20px' }}>
        {!user ? (
          <button onClick={() => login()}>Sign in with Google</button>
        ) : (
          <div>
            <p>Welcome, {user.name} ({user.email})</p>
            <img src={user.picture} alt="Profile" style={{ width: '50px', borderRadius: '50%' }} />
            <button onClick={handleSignOut}>Sign Out</button>
            <h3>Send Email</h3>
            <form onSubmit={handleSendEmail} style={{ marginTop: '20px' }}>
              <div>
                <label>Recipient Email:</label>
                <input
                  type="email"
                  name="to"
                  value={emailForm.to}
                  onChange={handleInputChange}
                  required
                  style={{ width: '100%', marginBottom: '10px' }}
                />
              </div>
              <div>
                <label>Subject:</label>
                <input
                  type="text"
                  name="subject"
                  value={emailForm.subject}
                  onChange={handleInputChange}
                  required
                  style={{ width: '100%', marginBottom: '10px' }}
                />
              </div>
              <div>
                <label>Body:</label>
                <textarea
                  name="body"
                  value={emailForm.body}
                  onChange={handleInputChange}
                  required
                  style={{ width: '100%', height: '100px', marginBottom: '10px' }}
                />
              </div>
              <button type="submit">Send</button>
            </form>
            {sendStatus && <p style={{ color: sendStatus.includes('successfully') ? 'green' : 'red' }}>{sendStatus}</p>}
          </div>
        )}
        {error && <p style={{ color: 'red' }}>{error}</p>}
      </div>
    </GoogleOAuthProvider>
  );
};

export default GoogleLoginButton;