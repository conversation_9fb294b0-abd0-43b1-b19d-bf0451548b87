import React, { useState } from 'react';
import { GoogleOAuthProvider, GoogleLogin, type CredentialResponse } from '@react-oauth/google'; // 直接导入
import axios from 'axios';

interface User {
  name: string;
  email: string;
  picture: string;
}

const GoogleLoginButton: React.FC = () => {
  const [user, setUser] = useState<User | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handleSuccess = async (response: CredentialResponse) => {
    if (response.credential) {
      console.log('编码后的 JWT ID 令牌：', response.credential);

      // 可选：客户端解码（不安全 - 仅用于开发）
      const decoded = JSON.parse(atob(response.credential.split('.')[1]));
      console.log('客户端解码：', decoded);

      // 将令牌发送到后端进行验证（后端代码不变）
      try {
        const res = await axios.post<{ success: boolean; user: User; token?: string }>('http://localhost:5000/api/auth/google', { id_token: response.credential });
        console.log('后端响应：', res.data);
        setUser(res.data.user);
        setError(null);
      } catch (err) {
        console.error('后端验证失败：', err);
        setError('登录失败。请重试。');
      }
    } else {
      console.error('响应中无凭证');
      setError('登录失败。');
    }
  };

  const handleError = () => {
    console.error('Google 登录失败');
    setError('登录失败。');
  };

  const handleSignOut = () => {
    setUser(null);
    console.log('用户已登出');
  };

  return (
    // 用 GoogleOAuthProvider 包裹组件，提供 Client ID
    <GoogleOAuthProvider clientId="522836786721-6880b8pn1mvkgl5ofl1hpqd9h6l68j7k.apps.googleusercontent.com">
      <div>
        <GoogleLogin
          onSuccess={handleSuccess}
          onError={handleError}
          theme="outline" // 自定义主题：outline 或 filled_blue
          size="large" // 大小：large/medium/small
          text="signin_with" // 文本：signin_with/signup_with/continue_with
          shape="rectangular" // 形状
        />
        {user && (
          <div>
            <p>欢迎，{user.name} ({user.email})</p>
            <img src={user.picture} alt="个人资料" style={{ width: '50px', borderRadius: '50%' }} />
            <button onClick={handleSignOut}>登出</button>
          </div>
        )}
        {error && <p style={{ color: 'red' }}>{error}</p>}
      </div>
    </GoogleOAuthProvider>
  );
};

export default GoogleLoginButton;