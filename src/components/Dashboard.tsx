import React, { useState, useEffect } from 'react';
import axios from 'axios';
import type { Email, SendEmailPayload } from '../types';

// 配置 axios 实例以发送凭据 (cookie)
const apiClient = axios.create({
  baseURL: 'http://127.0.0.1:5000',
  withCredentials: true, // 这是关键，确保 session cookie 被发送
});

const Dashboard: React.FC = () => {
  const [emails, setEmails] = useState<Email[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const [sendTo, setSendTo] = useState('');
  const [subject, setSubject] = useState('');
  const [body, setBody] = useState('');
  const [sendStatus, setSendStatus] = useState<string | null>(null);

  useEffect(() => {
    // 验证用户是否登录
    apiClient.get('/api/check_auth')
      .then(response => {
        if (!response.data.isLoggedIn) {
          window.location.href = '/'; // 如果未登录，重定向到登录页
        }
      })
      .catch(() => {
        window.location.href = '/';
      });
  }, []);

  const fetchEmails = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await apiClient.get<Email[]>('/api/emails');
      setEmails(response.data);
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to fetch emails.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSendEmail = async (e: React.FormEvent) => {
    e.preventDefault();
    setSendStatus('Sending...');
    const payload: SendEmailPayload = { to: sendTo, subject, body };
    try {
      const response = await apiClient.post('/api/send', payload);
      setSendStatus(`Success! Message ID: ${response.data.messageId}`);
      // 清空表单
      setSendTo('');
      setSubject('');
      setBody('');
    } catch (err: any) {
      setSendStatus(`Error: ${err.response?.data?.error || 'Failed to send email.'}`);
    }
  };

  const handleLogout = async () => {
    await apiClient.get('/auth/logout');
    window.location.href = '/';
  }

  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: 'auto' }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center'}}>
        <h1>Dashboard</h1>
        <button onClick={handleLogout}>Logout</button>
      </div>

      <hr />

      {/* 发送邮件部分 */}
      <div>
        <h2>Send an Email</h2>
        <form onSubmit={handleSendEmail} style={{ display: 'flex', flexDirection: 'column', gap: '10px' }}>
          <input
            type="email"
            placeholder="To"
            value={sendTo}
            onChange={(e) => setSendTo(e.target.value)}
            required
          />
          <input
            type="text"
            placeholder="Subject"
            value={subject}
            onChange={(e) => setSubject(e.target.value)}
            required
          />
          <textarea
            rows={5}
            placeholder="Body"
            value={body}
            onChange={(e) => setBody(e.target.value)}
            required
          />
          <button type="submit">Send Email</button>
        </form>
        {sendStatus && <p>{sendStatus}</p>}
      </div>

      <hr />

      {/* 阅读邮件部分 */}
      <div>
        <h2>Read Emails</h2>
        <button onClick={fetchEmails} disabled={isLoading}>
          {isLoading ? 'Loading...' : 'Fetch Latest 5 Emails'}
        </button>
        {error && <p style={{ color: 'red' }}>{error}</p>}
        <ul style={{ listStyle: 'none', padding: 0 }}>
          {emails.map((email) => (
            <li key={email.id} style={{ border: '1px solid #ccc', padding: '10px', margin: '10px 0' }}>
              <p><strong>From:</strong> {email.from}</p>
              <p><strong>Subject:</strong> {email.subject}</p>
              <p><em>{email.snippet}...</em></p>
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
};

export default Dashboard;
