import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { User, Email, SendEmailPayload, EmailListResponse, EmailDetailResponse } from '../types';

interface EmailManagerProps {
  user: User;
  onSignOut: () => void;
}

const EmailManager: React.FC<EmailManagerProps> = ({ user, onSignOut }) => {
  const [emails, setEmails] = useState<Email[]>([]);
  const [selectedEmail, setSelectedEmail] = useState<Email | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'inbox' | 'compose'>('inbox');
  
  // 发送邮件表单状态
  const [emailForm, setEmailForm] = useState({
    to: '',
    subject: '',
    body: '',
  });
  const [sendStatus, setSendStatus] = useState<string | null>(null);

  const API_BASE = 'http://localhost:5000/api';

  // 获取邮件列表
  const fetchEmails = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await axios.post<EmailListResponse>(`${API_BASE}/email/list`, {
        userEmail: user.email,
        maxResults: 20
      });
      
      if (response.data.success) {
        setEmails(response.data.emails);
      } else {
        setError('Failed to fetch emails');
      }
    } catch (err: any) {
      console.error('Fetch emails error:', err);
      setError('Failed to fetch emails. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // 获取邮件详情
  const fetchEmailDetail = async (emailId: string) => {
    setLoading(true);
    try {
      const response = await axios.post<EmailDetailResponse>(`${API_BASE}/email/${emailId}`, {
        userEmail: user.email
      });
      
      if (response.data.success) {
        setSelectedEmail(response.data.email);
      } else {
        setError('Failed to fetch email detail');
      }
    } catch (err: any) {
      console.error('Fetch email detail error:', err);
      setError('Failed to fetch email detail. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // 发送邮件
  const handleSendEmail = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!emailForm.to || !emailForm.subject || !emailForm.body) {
      setSendStatus('Please fill in all fields.');
      return;
    }

    setLoading(true);
    setSendStatus(null);
    try {
      const payload: SendEmailPayload = {
        ...emailForm,
        userEmail: user.email
      };

      const response = await axios.post(`${API_BASE}/email/send`, payload);
      
      if (response.data.success) {
        setSendStatus('Email sent successfully!');
        setEmailForm({ to: '', subject: '', body: '' });
        // 刷新邮件列表
        if (activeTab === 'inbox') {
          fetchEmails();
        }
      } else {
        setSendStatus('Failed to send email.');
      }
    } catch (err: any) {
      console.error('Send email error:', err);
      setSendStatus('Failed to send email. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // 处理表单输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setEmailForm(prev => ({ ...prev, [name]: value }));
  };

  // 组件挂载时获取邮件列表
  useEffect(() => {
    if (activeTab === 'inbox') {
      fetchEmails();
    }
  }, [activeTab]);

  const styles = {
    container: {
      maxWidth: '1200px',
      margin: '0 auto',
      padding: '20px',
      fontFamily: 'Arial, sans-serif'
    },
    header: {
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: '20px',
      padding: '10px',
      backgroundColor: '#f5f5f5',
      borderRadius: '5px'
    },
    userInfo: {
      display: 'flex',
      alignItems: 'center',
      gap: '10px'
    },
    avatar: {
      width: '40px',
      height: '40px',
      borderRadius: '50%'
    },
    tabs: {
      display: 'flex',
      marginBottom: '20px',
      borderBottom: '1px solid #ddd'
    },
    tab: {
      padding: '10px 20px',
      cursor: 'pointer',
      border: 'none',
      backgroundColor: 'transparent',
      borderBottom: '2px solid transparent'
    },
    activeTab: {
      borderBottom: '2px solid #007bff',
      color: '#007bff'
    },
    content: {
      display: 'flex',
      gap: '20px',
      height: '600px'
    },
    sidebar: {
      width: '300px',
      border: '1px solid #ddd',
      borderRadius: '5px',
      overflow: 'auto'
    },
    emailList: {
      listStyle: 'none',
      padding: '0',
      margin: '0'
    },
    emailItem: {
      padding: '15px',
      borderBottom: '1px solid #eee',
      cursor: 'pointer',
      transition: 'background-color 0.2s'
    },
    emailItemHover: {
      backgroundColor: '#f8f9fa'
    },
    selectedEmailItem: {
      backgroundColor: '#e3f2fd'
    },
    main: {
      flex: 1,
      border: '1px solid #ddd',
      borderRadius: '5px',
      padding: '20px',
      overflow: 'auto'
    },
    form: {
      display: 'flex',
      flexDirection: 'column' as const,
      gap: '15px'
    },
    input: {
      padding: '10px',
      border: '1px solid #ddd',
      borderRadius: '4px',
      fontSize: '14px'
    },
    textarea: {
      padding: '10px',
      border: '1px solid #ddd',
      borderRadius: '4px',
      fontSize: '14px',
      minHeight: '200px',
      resize: 'vertical' as const
    },
    button: {
      padding: '10px 20px',
      backgroundColor: '#007bff',
      color: 'white',
      border: 'none',
      borderRadius: '4px',
      cursor: 'pointer',
      fontSize: '14px'
    },
    buttonSecondary: {
      backgroundColor: '#6c757d'
    },
    error: {
      color: '#dc3545',
      padding: '10px',
      backgroundColor: '#f8d7da',
      border: '1px solid #f5c6cb',
      borderRadius: '4px'
    },
    success: {
      color: '#155724',
      padding: '10px',
      backgroundColor: '#d4edda',
      border: '1px solid #c3e6cb',
      borderRadius: '4px'
    },
    loading: {
      textAlign: 'center' as const,
      padding: '20px',
      color: '#6c757d'
    }
  };

  return (
    <div style={styles.container}>
      {/* 头部 */}
      <div style={styles.header}>
        <div style={styles.userInfo}>
          <img src={user.picture} alt="Profile" style={styles.avatar} />
          <div>
            <div><strong>{user.name}</strong></div>
            <div style={{ fontSize: '12px', color: '#666' }}>{user.email}</div>
          </div>
        </div>
        <button 
          onClick={onSignOut}
          style={{...styles.button, ...styles.buttonSecondary}}
        >
          Sign Out
        </button>
      </div>

      {/* 标签页 */}
      <div style={styles.tabs}>
        <button
          style={{
            ...styles.tab,
            ...(activeTab === 'inbox' ? styles.activeTab : {})
          }}
          onClick={() => setActiveTab('inbox')}
        >
          Inbox
        </button>
        <button
          style={{
            ...styles.tab,
            ...(activeTab === 'compose' ? styles.activeTab : {})
          }}
          onClick={() => setActiveTab('compose')}
        >
          Compose
        </button>
      </div>

      {/* 错误提示 */}
      {error && (
        <div style={styles.error}>
          {error}
        </div>
      )}

      {/* 主要内容 */}
      <div style={styles.content}>
        {activeTab === 'inbox' ? (
          <>
            {/* 邮件列表 */}
            <div style={styles.sidebar}>
              {loading && <div style={styles.loading}>Loading emails...</div>}
              <ul style={styles.emailList}>
                {emails.map((email) => (
                  <li
                    key={email.id}
                    style={{
                      ...styles.emailItem,
                      ...(selectedEmail?.id === email.id ? styles.selectedEmailItem : {})
                    }}
                    onClick={() => fetchEmailDetail(email.id)}
                    onMouseEnter={(e) => {
                      if (selectedEmail?.id !== email.id) {
                        e.currentTarget.style.backgroundColor = '#f8f9fa';
                      }
                    }}
                    onMouseLeave={(e) => {
                      if (selectedEmail?.id !== email.id) {
                        e.currentTarget.style.backgroundColor = 'transparent';
                      }
                    }}
                  >
                    <div style={{ fontWeight: 'bold', marginBottom: '5px' }}>
                      {email.subject || '(No Subject)'}
                    </div>
                    <div style={{ fontSize: '12px', color: '#666', marginBottom: '5px' }}>
                      From: {email.from}
                    </div>
                    <div style={{ fontSize: '12px', color: '#999' }}>
                      {email.date}
                    </div>
                    <div style={{ fontSize: '12px', color: '#666', marginTop: '5px' }}>
                      {email.snippet}
                    </div>
                  </li>
                ))}
              </ul>
            </div>

            {/* 邮件详情 */}
            <div style={styles.main}>
              {loading && <div style={styles.loading}>Loading email detail...</div>}
              {selectedEmail ? (
                <div>
                  <h2>{selectedEmail.subject || '(No Subject)'}</h2>
                  <div style={{ marginBottom: '20px', fontSize: '14px', color: '#666' }}>
                    <div><strong>From:</strong> {selectedEmail.from}</div>
                    {selectedEmail.to && <div><strong>To:</strong> {selectedEmail.to}</div>}
                    <div><strong>Date:</strong> {selectedEmail.date}</div>
                  </div>
                  <div style={{ 
                    padding: '15px', 
                    backgroundColor: '#f8f9fa', 
                    borderRadius: '5px',
                    whiteSpace: 'pre-wrap',
                    lineHeight: '1.5'
                  }}>
                    {selectedEmail.body || selectedEmail.snippet}
                  </div>
                </div>
              ) : (
                <div style={styles.loading}>
                  Select an email to view its content
                </div>
              )}
            </div>
          </>
        ) : (
          /* 撰写邮件 */
          <div style={styles.main}>
            <h2>Compose Email</h2>
            <form onSubmit={handleSendEmail} style={styles.form}>
              <input
                type="email"
                name="to"
                placeholder="Recipient email"
                value={emailForm.to}
                onChange={handleInputChange}
                style={styles.input}
                required
              />
              <input
                type="text"
                name="subject"
                placeholder="Subject"
                value={emailForm.subject}
                onChange={handleInputChange}
                style={styles.input}
                required
              />
              <textarea
                name="body"
                placeholder="Email body"
                value={emailForm.body}
                onChange={handleInputChange}
                style={styles.textarea}
                required
              />
              <button 
                type="submit" 
                style={styles.button}
                disabled={loading}
              >
                {loading ? 'Sending...' : 'Send Email'}
              </button>
            </form>
            
            {sendStatus && (
              <div style={sendStatus.includes('successfully') ? styles.success : styles.error}>
                {sendStatus}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default EmailManager;
