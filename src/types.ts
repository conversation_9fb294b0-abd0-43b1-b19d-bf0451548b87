export interface User {
  name: string;
  email: string;
  picture: string;
}

export interface Email {
  id: string;
  subject: string;
  from: string;
  to?: string;
  date: string;
  snippet: string;
  body?: string;
}

export interface SendEmailPayload {
  to: string;
  subject: string;
  body: string;
  userEmail: string;
}

export interface EmailListResponse {
  success: boolean;
  emails: Email[];
  total: number;
}

export interface EmailDetailResponse {
  success: boolean;
  email: Email;
}
